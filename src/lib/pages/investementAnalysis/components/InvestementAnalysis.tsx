import {
  <PERSON>,
  CardBody,
  CardHeader,
  Progress,
  useSteps,
  VStack,
  Heading,
  Box,
} from '@chakra-ui/react';
import { useRef, useState } from 'react';
import type { FormDataInvestementAnalysistSteps } from '~/lib/types/investement-analysis';
import { FormWrapper } from './FormWrapper';
import { StepperComponent } from '~/lib/components/commonComponents/multistep/StepperComponent';
import { StepperNavigationButtons } from '~/lib/components/commonComponents/multistep/StepperNavigationButtons';
import RegimeDetailsModal from './common/RegimeDetailsModal';

export default function InvestementAnalysis() {
  const steps = [
    { title: 'PROFIL INVESTISSEUR' },
    { title: 'EMPRUNT' },
    { title: "CAS D'ÉTUDE" },
    { title: 'BIENS IMMOBILIERS' },
    { title: 'SYNTHÈSE' },
  ];

  // State for modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRegime, setSelectedRegime] = useState<string | null>(null);

  // Function to handle regime click
  const handleRegimeClick = (regime: string) => {
    setSelectedRegime(regime);
    setIsModalOpen(true);
  };

  const [formData, setFormData] = useState<FormDataInvestementAnalysistSteps>({
    profileInvestorData: {
      "buyingOption": "Seul",
      "isMarried": "false",
      "regimeMatrimonial": "",
      "numberOfPerson": "",
      "ownsRealEstate": "true",
      "numberOfRealEstate": "",
      "assetsInfoTable": [
          {
              "number": 1,
              "rentalMode": "meublee",
              "annualRent": "14400",
              "legalStatus": "indivision",
              "specialTaxStatus": "lmnp",
              "applicableTaxRegime": "bic_reel_simplifie"
          },
          {
              "number": 2,
              "rentalMode": "meublee",
              "annualRent": "12000",
              "legalStatus": "sci_ir",
              "specialTaxStatus": "lmp",
              "applicableTaxRegime": "fonciers_reel"
          }
      ],
      "taxRegimeTotals": {
          "bic_reel_simplifie": 14400,
          "fonciers_reel": 12000
      }
  },
    realEstateData: {
      "travauxAmeliorationMontant": 0,
      "travauxReparationMontant": 0,
      "travauxTotal": 0,
      "prixVenteMontant": 0,
      "prixAcquisitionMontant": 0,
      "fraisAcquisitionForfait": 0,
      "travauxDepensesForfait": 0,
      "capitalSocialSCI": 0,
      "locationsSaisonnieres": "Non",
      "locationsNues": "Oui",
      "locationsMeublees": "Oui",
      "applicationIndice": "Oui",
      "nombreAssocies": 0,
      "revenusPartsSCI": 0,
      "recettesExceptionnelles": 0,
      "recettesEmplacementsPublicitaires": 0,
      "sarlLocationsMeublees": "Oui",
      "sarlAssociesLigneDirecte": "Non",
      "sarlAssociesFreresSoeurs": "Oui",
      "sarlAssociesConjointsPACS": "Non",
      "garantieEviction": "Non",
      "diagnosticsObligatoires": "Non",
      "chargesIndemnites": "Non",
      "fraisAcquisitionReels": "Non",
      "fraisAcquisitionForfaitaire": "Oui",
      "depenseTravauxReelles": "Non",
      "fraisVoirie": "Non",
      "travauxDeficitFoncier": "Oui",
      "travauxConstruction": "Non",
      "travauxConstructionMontant": 0,
      "travauxAmeliorationDeductible": "Oui",
      "travauxReparationDeductible": "Oui"
  },
    investementCalculatorData: {
      "montantInvestissement": 216000,
      "dontTerrain": 0.15,
      "dontConstruction": 0.85,
      "coutAcquisition": 216000,
      "travaux": 0,
      "revenuFiscalReference": 50000,
      "tauxMarginalImposition": 0.11,
      "nombreParts": 2,
      "loyersAnnuelHorsCharges": 13200,
      "chargesComprises": 1800,
      "chargesLocatives": 1800,
      "assurancesNonOccupant": 300,
      "assurancesEmprunteur": 96,
      "chargesEntretien": 140,
      "taxesFoncieres": 1450,
      "fraisAgences": 0,
      "fraisEmprunts": 1000,
      "CFE": 400,
      "fraisBancaires": 40,
      "interetsEmprunt": 2559,
      "travauxDeductibles": 0,
      "quotePartTerrain": 0.15,
      "quotePartGrosOeuvre": 0.4,
      "quotePartFacade": 0.05,
      "quotePartIGT": 0.2,
      "quotePartAgencements": 0.2,
      "valeurMeubles": 0,
      "dureeGrosOeuvre": 50,
      "dureeFacade": 20,
      "dureeIGT": 15,
      "dureeAgencements": 8,
      "dureeMeubles": 5,
      "dateAcquisition": "2020-12-31T00:00:00.000Z",
      "dureeDetention": 20,
      "regime": "reel",
      "fraisComptables": 0,
      "fraisJuridiques": 0,
      "fraisGerant": 0,
      "monthlyPayment": 789.2156376485021,
      "loanAmount": 200000,
  },
  });
  const formRefs = steps.map(() => useRef<any>(null));

  const { activeStep, setActiveStep } = useSteps({
    index: 0,
    count: steps.length,
  });
  const isLastStep = activeStep === steps.length - 1;

  const onNextStep = async () => {
    const currentFormRef = formRefs[activeStep];
    const isValid = await currentFormRef.current?.onSubmit();

    // If moving to the synthèse step (step 4), ensure all calculations are performed
    if (isValid && activeStep + 1 < steps.length) {
      // If we're about to navigate to the synthèse step
      if (activeStep + 1 === 4) {
        console.log(
          'Navigating to synthèse step, ensuring all calculations are performed'
        );
        // The SyntheseForm component will handle the calculations when it mounts
      }

      setActiveStep(activeStep + 1);
    }
  };
  const validateBeforeSetActiveStep = async (nextStep: number) => {
    const currentFormRef = formRefs[activeStep];
    const isValid = await currentFormRef.current?.onSubmit();

    // If navigating directly to the synthèse step (step 4)
    if (isValid) {
      if (nextStep === 4) {
        console.log(
          'Directly navigating to synthèse step, ensuring all calculations are performed'
        );
        // The SyntheseForm component will handle the calculations when it mounts
      }

      setActiveStep(nextStep);
    }
  };

  const previousStep = () => {
    setActiveStep(Math.max(activeStep - 1, 0));
    formRefs[activeStep - 1].current?.setDataFromParent(formData);
  };
  const handleFormDataChange = <
    T extends keyof FormDataInvestementAnalysistSteps,
  >(
    key: T,
    data: FormDataInvestementAnalysistSteps[T]
  ) => {
    setFormData({ ...formData, [key]: data });
    console.log(formData)
  };
  return (
    <Box maxW="container.xl" mx="auto" py={8}>
      <Card w="100%" variant="outline" as="form" boxShadow="md">
        <CardHeader bg="white" borderTopRadius="md">
          <Heading size="md" textAlign="center" color="gray.700">
            Analyse d'investissement
          </Heading>
          <Progress
            value={(activeStep / (steps.length - 1)) * 100}
            size="xs"
            colorScheme="teal"
            mt={4}
          />
        </CardHeader>
        <CardBody bg="white">
          <VStack spacing={6} align="center" width="100%">
            <StepperComponent
              steps={steps}
              activeStep={activeStep}
              setActiveStep={validateBeforeSetActiveStep}
            />
            <Box width="100%">
              <FormWrapper
                activeStep={activeStep}
                formRefs={formRefs}
                formData={formData}
                handleFormDataChange={handleFormDataChange}
                onRegimeClick={handleRegimeClick}
              />
            </Box>
            <StepperNavigationButtons
              isLastStep={isLastStep}
              onNextStep={onNextStep}
              previousStep={previousStep}
              activeStep={activeStep}
            />
          </VStack>
        </CardBody>
      </Card>

      {/* Modal for regime details */}
      {isModalOpen && (
        <RegimeDetailsModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          selectedRegime={selectedRegime}
          data={formData}
        />
      )}
    </Box>
  );
}
